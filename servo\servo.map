Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(.text) for Reset_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel3_IRQHandler) for DMA1_Channel3_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) for DMA1_Channel5_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.TIM1_UP_IRQHandler) for TIM1_UP_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f103xb.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xb.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to bsp_system.o(i.servo_system_init) for servo_system_init
    main.o(i.main) refers to servo_app.o(i.Interpolation_Init) for Interpolation_Init
    main.o(i.main) refers to pid.o(i.PID_Init) for PID_Init
    main.o(i.main) refers to servo_app.o(i.Tripod_PID_Task1) for Tripod_PID_Task1
    main.o(i.main) refers to servo_app.o(i.Tripod_PID_Interpolation_Line) for Tripod_PID_Interpolation_Line
    main.o(i.main) refers to servo_app.o(i.Tripod_PID_Text) for Tripod_PID_Text
    main.o(i.main) refers to task.o(i.task_run) for task_run
    main.o(i.main) refers to laser_app.o(i.Laser_ON) for Laser_ON
    main.o(i.main) refers to pid.o(.bss) for pid_x
    main.o(i.main) refers to pid.o(.bss) for pid_y
    main.o(i.main) refers to uart_app.o(.data) for Task1_Start
    main.o(i.main) refers to uart_app.o(.bss) for Task1_step
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dma.o(i.MX_DMA_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to uart_app.o(.bss) for uart1_rx_dma_buf
    usart.o(i.MX_USART3_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to uart_app.o(.bss) for uart3_rx_dma_buf
    usart.o(i.fputc) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for .bss
    stm32f1xx_it.o(i.DMA1_Channel3_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel3_IRQHandler) refers to usart.o(.bss) for hdma_usart3_rx
    stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f1xx_it.o(i.TIM1_UP_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f1xx_it.o(i.TIM1_UP_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    stm32f1xx_it.o(i.TIM1_UP_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT) for HAL_TIM_Base_Stop_IT
    stm32f1xx_it.o(i.TIM1_UP_IRQHandler) refers to tim.o(.bss) for htim1
    stm32f1xx_it.o(i.TIM1_UP_IRQHandler) refers to stm32f1xx_it.o(.bss) for .bss
    stm32f1xx_it.o(i.TIM2_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f1xx_it.o(i.TIM2_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    stm32f1xx_it.o(i.TIM2_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT) for HAL_TIM_Base_Stop_IT
    stm32f1xx_it.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for htim2
    stm32f1xx_it.o(i.TIM2_IRQHandler) refers to stm32f1xx_it.o(.bss) for .bss
    stm32f1xx_it.o(i.TIM3_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f1xx_it.o(i.TIM3_IRQHandler) refers to servo_app.o(i.Interpolation_Step) for Interpolation_Step
    stm32f1xx_it.o(i.TIM3_IRQHandler) refers to tim.o(.bss) for htim3
    stm32f1xx_it.o(i.TIM3_IRQHandler) refers to servo_app.o(.bss) for interp
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to uart_app.o(.bss) for uart1_rx_dma_buf
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    stm32f1xx_it.o(i.USART3_IRQHandler) refers to uart_app.o(.bss) for uart3_rx_dma_buf
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAError) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal_rcc.o(i.RCC_Delay) for RCC_Delay
    stm32f1xx_hal_rcc.o(i.RCC_Delay) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for .constdata
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) for FLASH_OB_ProgramData
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    servo_app.o(i.Interpolation_Init) refers to servo_app.o(.bss) for .bss
    servo_app.o(i.Interpolation_Step) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT) for HAL_TIM_Base_Stop_IT
    servo_app.o(i.Interpolation_Step) refers to servo_app.o(i.Tripod_PID_Goto) for Tripod_PID_Goto
    servo_app.o(i.Interpolation_Step) refers to servo_app.o(.bss) for .bss
    servo_app.o(i.Interpolation_Step) refers to servo_app.o(.data) for .data
    servo_app.o(i.Interpolation_Step) refers to tim.o(.bss) for htim3
    servo_app.o(i.Interpolation_Step) refers to stm32f1xx_it.o(.bss) for Tripod_X
    servo_app.o(i.Step_Motor_X_Cmd) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    servo_app.o(i.Step_Motor_X_Dir) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    servo_app.o(i.Step_Motor_X_DisCmd) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    servo_app.o(i.Step_Motor_Y_Cmd) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    servo_app.o(i.Step_Motor_Y_Dir) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    servo_app.o(i.Step_Motor_Y_DisCmd) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    servo_app.o(i.Tripod_Interpolation_Move) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    servo_app.o(i.Tripod_Interpolation_Move) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    servo_app.o(i.Tripod_Interpolation_Move) refers to servo_app.o(.bss) for .bss
    servo_app.o(i.Tripod_Interpolation_Move) refers to stm32f1xx_it.o(.bss) for Tripod_X
    servo_app.o(i.Tripod_Interpolation_Move) refers to servo_app.o(.data) for .data
    servo_app.o(i.Tripod_Interpolation_Move) refers to tim.o(.bss) for htim3
    servo_app.o(i.Tripod_PID_Goto) refers to fflti.o(.text) for __aeabi_i2f
    servo_app.o(i.Tripod_PID_Goto) refers to pid.o(i.PID_Calculate) for PID_Calculate
    servo_app.o(i.Tripod_PID_Goto) refers to fmul.o(.text) for __aeabi_fmul
    servo_app.o(i.Tripod_PID_Goto) refers to fadd.o(.text) for __aeabi_fadd
    servo_app.o(i.Tripod_PID_Goto) refers to servo_app.o(i.Tripod_X_Angle) for Tripod_X_Angle
    servo_app.o(i.Tripod_PID_Goto) refers to servo_app.o(i.Tripod_Y_Angle) for Tripod_Y_Angle
    servo_app.o(i.Tripod_PID_Goto) refers to ffixi.o(.text) for __aeabi_f2iz
    servo_app.o(i.Tripod_PID_Goto) refers to servo_app.o(.bss) for .bss
    servo_app.o(i.Tripod_PID_Goto) refers to servo_app.o(.data) for .data
    servo_app.o(i.Tripod_PID_Goto) refers to pid.o(.bss) for pid_x
    servo_app.o(i.Tripod_PID_Goto) refers to pid.o(.bss) for pid_y
    servo_app.o(i.Tripod_PID_Goto) refers to stm32f1xx_it.o(.bss) for Tripod_X
    servo_app.o(i.Tripod_PID_Interpolation_Line) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    servo_app.o(i.Tripod_PID_Interpolation_Line) refers to servo_app.o(i.Tripod_Interpolation_Move) for Tripod_Interpolation_Move
    servo_app.o(i.Tripod_PID_Interpolation_Line) refers to servo_app.o(.bss) for .bss
    servo_app.o(i.Tripod_PID_Interpolation_Line) refers to servo_app.o(.data) for .data
    servo_app.o(i.Tripod_PID_Interpolation_Line) refers to uart_app.o(.data) for Task1_Start
    servo_app.o(i.Tripod_PID_Task1) refers to fflti.o(.text) for __aeabi_i2f
    servo_app.o(i.Tripod_PID_Task1) refers to pid.o(i.PID_Calculate) for PID_Calculate
    servo_app.o(i.Tripod_PID_Task1) refers to fmul.o(.text) for __aeabi_fmul
    servo_app.o(i.Tripod_PID_Task1) refers to fadd.o(.text) for __aeabi_fadd
    servo_app.o(i.Tripod_PID_Task1) refers to servo_app.o(i.Tripod_X_Angle) for Tripod_X_Angle
    servo_app.o(i.Tripod_PID_Task1) refers to servo_app.o(i.Tripod_Y_Angle) for Tripod_Y_Angle
    servo_app.o(i.Tripod_PID_Task1) refers to ffixi.o(.text) for __aeabi_f2iz
    servo_app.o(i.Tripod_PID_Task1) refers to uart_app.o(.bss) for Task1_step
    servo_app.o(i.Tripod_PID_Task1) refers to servo_app.o(.data) for .data
    servo_app.o(i.Tripod_PID_Task1) refers to pid.o(.bss) for pid_x
    servo_app.o(i.Tripod_PID_Task1) refers to pid.o(.bss) for pid_y
    servo_app.o(i.Tripod_PID_Task1) refers to stm32f1xx_it.o(.bss) for Tripod_X
    servo_app.o(i.Tripod_PID_Text) refers to pid.o(i.PID_Init) for PID_Init
    servo_app.o(i.Tripod_PID_Text) refers to fflti.o(.text) for __aeabi_i2f
    servo_app.o(i.Tripod_PID_Text) refers to pid.o(i.PID_Calculate) for PID_Calculate
    servo_app.o(i.Tripod_PID_Text) refers to fmul.o(.text) for __aeabi_fmul
    servo_app.o(i.Tripod_PID_Text) refers to fadd.o(.text) for __aeabi_fadd
    servo_app.o(i.Tripod_PID_Text) refers to servo_app.o(i.Tripod_X_Angle) for Tripod_X_Angle
    servo_app.o(i.Tripod_PID_Text) refers to servo_app.o(i.Tripod_Y_Angle) for Tripod_Y_Angle
    servo_app.o(i.Tripod_PID_Text) refers to ffixi.o(.text) for __aeabi_f2iz
    servo_app.o(i.Tripod_PID_Text) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    servo_app.o(i.Tripod_PID_Text) refers to uart_app.o(.data) for Task_Stop_num
    servo_app.o(i.Tripod_PID_Text) refers to pid.o(.bss) for pid_x
    servo_app.o(i.Tripod_PID_Text) refers to pid.o(.bss) for pid_y
    servo_app.o(i.Tripod_PID_Text) refers to servo_app.o(.data) for .data
    servo_app.o(i.Tripod_PID_Text) refers to servo_app.o(.bss) for .bss
    servo_app.o(i.Tripod_PID_Text) refers to stm32f1xx_it.o(.bss) for Tripod_X
    servo_app.o(i.Tripod_PID_Text) refers to usart.o(.bss) for huart3
    servo_app.o(i.Tripod_X_Abs_Step) refers to fflti.o(.text) for __aeabi_i2f
    servo_app.o(i.Tripod_X_Abs_Step) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    servo_app.o(i.Tripod_X_Abs_Step) refers to servo_app.o(i.Step_Motor_X_Dir) for Step_Motor_X_Dir
    servo_app.o(i.Tripod_X_Abs_Step) refers to fadd.o(.text) for __aeabi_fsub
    servo_app.o(i.Tripod_X_Abs_Step) refers to ffixui.o(.text) for __aeabi_f2uiz
    servo_app.o(i.Tripod_X_Abs_Step) refers to servo_app.o(i.Tripod_X_Step) for Tripod_X_Step
    servo_app.o(i.Tripod_X_Abs_Step) refers to cfcmple.o(.text) for __aeabi_cfcmple
    servo_app.o(i.Tripod_X_Abs_Step) refers to stm32f1xx_it.o(.bss) for Tripod_X
    servo_app.o(i.Tripod_X_Angle) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    servo_app.o(i.Tripod_X_Angle) refers to servo_app.o(i.Step_Motor_X_Dir) for Step_Motor_X_Dir
    servo_app.o(i.Tripod_X_Angle) refers to fadd.o(.text) for __aeabi_fsub
    servo_app.o(i.Tripod_X_Angle) refers to fmul.o(.text) for __aeabi_fmul
    servo_app.o(i.Tripod_X_Angle) refers to ffixui.o(.text) for __aeabi_f2uiz
    servo_app.o(i.Tripod_X_Angle) refers to servo_app.o(i.Tripod_X_Step) for Tripod_X_Step
    servo_app.o(i.Tripod_X_Angle) refers to stm32f1xx_it.o(.bss) for Tripod_X
    servo_app.o(i.Tripod_X_Step) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    servo_app.o(i.Tripod_X_Step) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    servo_app.o(i.Tripod_X_Step) refers to stm32f1xx_it.o(.bss) for Tripod_X
    servo_app.o(i.Tripod_X_Step) refers to tim.o(.bss) for htim1
    servo_app.o(i.Tripod_Y_Abs_Step) refers to fflti.o(.text) for __aeabi_i2f
    servo_app.o(i.Tripod_Y_Abs_Step) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    servo_app.o(i.Tripod_Y_Abs_Step) refers to servo_app.o(i.Step_Motor_Y_Dir) for Step_Motor_Y_Dir
    servo_app.o(i.Tripod_Y_Abs_Step) refers to fadd.o(.text) for __aeabi_fsub
    servo_app.o(i.Tripod_Y_Abs_Step) refers to ffixui.o(.text) for __aeabi_f2uiz
    servo_app.o(i.Tripod_Y_Abs_Step) refers to servo_app.o(i.Tripod_Y_Step) for Tripod_Y_Step
    servo_app.o(i.Tripod_Y_Abs_Step) refers to cfcmple.o(.text) for __aeabi_cfcmple
    servo_app.o(i.Tripod_Y_Abs_Step) refers to stm32f1xx_it.o(.bss) for Tripod_Y
    servo_app.o(i.Tripod_Y_Angle) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    servo_app.o(i.Tripod_Y_Angle) refers to servo_app.o(i.Step_Motor_Y_Dir) for Step_Motor_Y_Dir
    servo_app.o(i.Tripod_Y_Angle) refers to fadd.o(.text) for __aeabi_fsub
    servo_app.o(i.Tripod_Y_Angle) refers to fmul.o(.text) for __aeabi_fmul
    servo_app.o(i.Tripod_Y_Angle) refers to ffixui.o(.text) for __aeabi_f2uiz
    servo_app.o(i.Tripod_Y_Angle) refers to servo_app.o(i.Tripod_Y_Step) for Tripod_Y_Step
    servo_app.o(i.Tripod_Y_Angle) refers to stm32f1xx_it.o(.bss) for Tripod_Y
    servo_app.o(i.Tripod_Y_Step) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    servo_app.o(i.Tripod_Y_Step) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    servo_app.o(i.Tripod_Y_Step) refers to stm32f1xx_it.o(.bss) for Tripod_Y
    servo_app.o(i.Tripod_Y_Step) refers to tim.o(.bss) for htim2
    laser_app.o(i.Laser_OFF) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    laser_app.o(i.Laser_ON) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to uart_app.o(.data) for .data
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to uart_app.o(.bss) for .bss
    uart_app.o(i.uart3_task) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(i.uart3_task) refers to uart_app.o(.data) for .data
    uart_app.o(i.uart3_task) refers to uart_app.o(.bss) for .bss
    uart_app.o(i.uart3_task) refers to servo_app.o(.data) for current_x
    uart_app.o(i.uart3_task) refers to servo_app.o(.bss) for PID_text
    uart_app.o(i.uart_task) refers to servo_app.o(i.Step_Motor_X_Dir) for Step_Motor_X_Dir
    uart_app.o(i.uart_task) refers to printf1.o(i.__0printf$1) for __2printf
    uart_app.o(i.uart_task) refers to servo_app.o(i.Tripod_X_Abs_Step) for Tripod_X_Abs_Step
    uart_app.o(i.uart_task) refers to servo_app.o(i.Step_Motor_Y_Dir) for Step_Motor_Y_Dir
    uart_app.o(i.uart_task) refers to servo_app.o(i.Tripod_Y_Abs_Step) for Tripod_Y_Abs_Step
    uart_app.o(i.uart_task) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    uart_app.o(i.uart_task) refers to strncmp.o(.text) for strncmp
    uart_app.o(i.uart_task) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(i.uart_task) refers to uart_app.o(.data) for .data
    uart_app.o(i.uart_task) refers to uart_app.o(.bss) for .bss
    uart_app.o(i.uart_task) refers to usart.o(.bss) for huart3
    uart_app.o(i.uart_task) refers to servo_app.o(.data) for current_x
    pid.o(i.PID_Calculate) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(i.PID_Calculate) refers to cfrcmple.o(.text) for __aeabi_cfrcmple
    pid.o(i.PID_Calculate) refers to fmul.o(.text) for __aeabi_fmul
    task.o(i.task_run) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    task.o(i.task_run) refers to task.o(.data) for .data
    task.o(.data) refers to uart_app.o(i.uart_task) for uart_task
    task.o(.data) refers to uart_app.o(i.uart3_task) for uart3_task
    bsp_system.o(i.servo_system_init) refers to servo_app.o(i.Step_Motor_X_Cmd) for Step_Motor_X_Cmd
    bsp_system.o(i.servo_system_init) refers to servo_app.o(i.Step_Motor_Y_Cmd) for Step_Motor_Y_Cmd
    bsp_system.o(i.servo_system_init) refers to servo_app.o(i.Step_Motor_X_Dir) for Step_Motor_X_Dir
    bsp_system.o(i.servo_system_init) refers to servo_app.o(i.Step_Motor_Y_Dir) for Step_Motor_Y_Dir
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cfcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cfrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f103xb.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (80 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (108 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (72 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start), (88 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (234 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (452 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (452 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (134 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (134 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (64 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (168 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (496 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (208 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (170 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (234 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (210 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetChannelState), (36 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (192 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (72 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init), (74 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (476 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (300 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop), (114 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (208 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (186 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (72 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init), (74 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (448 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop), (116 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (212 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (258 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (64 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (100 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (120 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (100 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (216 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (72 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init), (74 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (448 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (116 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (212 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (44 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (80 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (80 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (62 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (62 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (24 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt), (24 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig), (96 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig), (100 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig), (96 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig), (72 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig), (88 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI3_SetConfig), (50 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI4_SetConfig), (56 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (36 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (64 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (210 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (132 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (200 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (144 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (396 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (106 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (180 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (178 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (98 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (118 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (100 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (396 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (106 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (180 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (178 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (228 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (60 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (148 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (44 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (168 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (268 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (236 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (36 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (14 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (96 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (556 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (76 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (86 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (40 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (40 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (86 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (28 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (28 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (92 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (84 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (272 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (140 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (80 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (180 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (180 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData), (64 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (68 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (168 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (76 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (28 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (124 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (16 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (180 bytes).
    Removing stm32f1xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init), (112 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_Init), (132 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_SendBreak), (58 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (60 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (60 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init), (146 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (248 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (86 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort), (216 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (168 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (116 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT), (248 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DeInit), (52 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetState), (12 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive), (178 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (124 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback), (46 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback), (46 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT), (56 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (124 bytes).
    Removing servo_app.o(.rev16_text), (4 bytes).
    Removing servo_app.o(.revsh_text), (4 bytes).
    Removing servo_app.o(.rrx_text), (6 bytes).
    Removing servo_app.o(i.Step_Motor_X_DisCmd), (16 bytes).
    Removing servo_app.o(i.Step_Motor_Y_DisCmd), (16 bytes).
    Removing servo_app.o(i.Tripod_X_Coord), (2 bytes).
    Removing servo_app.o(i.Tripod_Y_Coord), (2 bytes).
    Removing laser_app.o(.rev16_text), (4 bytes).
    Removing laser_app.o(.revsh_text), (4 bytes).
    Removing laser_app.o(.rrx_text), (6 bytes).
    Removing laser_app.o(i.Laser_OFF), (16 bytes).
    Removing uart_app.o(.rev16_text), (4 bytes).
    Removing uart_app.o(.revsh_text), (4 bytes).
    Removing uart_app.o(.rrx_text), (6 bytes).
    Removing pid.o(.rev16_text), (4 bytes).
    Removing pid.o(.revsh_text), (4 bytes).
    Removing pid.o(.rrx_text), (6 bytes).
    Removing task.o(.rev16_text), (4 bytes).
    Removing task.o(.revsh_text), (4 bytes).
    Removing task.o(.rrx_text), (6 bytes).
    Removing task.o(.data), (2 bytes).
    Removing task.o(.data), (2 bytes).
    Removing task.o(.data), (2 bytes).
    Removing bsp_system.o(.rev16_text), (4 bytes).
    Removing bsp_system.o(.revsh_text), (4 bytes).
    Removing bsp_system.o(.rrx_text), (6 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing dmul.o(.text), (228 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).
    Removing depilogue.o(.text), (186 bytes).

403 unused section(s) (total 27422 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ..\HardWare\PID\PID.c                    0x00000000   Number         0  pid.o ABSOLUTE
    ..\HardWare\laser_app.c                  0x00000000   Number         0  laser_app.o ABSOLUTE
    ..\HardWare\servo_app.c                  0x00000000   Number         0  servo_app.o ABSOLUTE
    ..\HardWare\uart_app.c                   0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\\HardWare\\PID\\PID.c                 0x00000000   Number         0  pid.o ABSOLUTE
    ..\\HardWare\\laser_app.c                0x00000000   Number         0  laser_app.o ABSOLUTE
    ..\\HardWare\\servo_app.c                0x00000000   Number         0  servo_app.o ABSOLUTE
    ..\\HardWare\\uart_app.c                 0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\\system\\bsp_system.c                 0x00000000   Number         0  bsp_system.o ABSOLUTE
    ..\\system\\task.c                       0x00000000   Number         0  task.o ABSOLUTE
    ..\system\bsp_system.c                   0x00000000   Number         0  bsp_system.o ABSOLUTE
    ..\system\task.c                         0x00000000   Number         0  task.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    cfcmple.s                                0x00000000   Number         0  cfcmple.o ABSOLUTE
    cfrcmple.s                               0x00000000   Number         0  cfrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f103xb.s                    0x00000000   Number         0  startup_stm32f103xb.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f103xb.o(RESET)
    .ARM.Collect$$$$00000000                 0x080000ec   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080000ec   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080000f0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080000f4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080000f4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080000f4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080000fc   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x08000100   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x08000100   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x08000100   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000100   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000104   Section       36  startup_stm32f103xb.o(.text)
    .text                                    0x08000128   Section        0  memseta.o(.text)
    .text                                    0x0800014c   Section        0  strncmp.o(.text)
    .text                                    0x0800016a   Section        0  fadd.o(.text)
    .text                                    0x0800021a   Section        0  fmul.o(.text)
    .text                                    0x0800027e   Section        0  fflti.o(.text)
    .text                                    0x08000290   Section        0  ffixi.o(.text)
    .text                                    0x080002c2   Section        0  ffixui.o(.text)
    .text                                    0x080002ec   Section       20  cfcmple.o(.text)
    .text                                    0x08000300   Section       20  cfrcmple.o(.text)
    .text                                    0x08000314   Section        0  uidiv.o(.text)
    .text                                    0x08000340   Section        0  iusefp.o(.text)
    .text                                    0x08000340   Section        0  fepilogue.o(.text)
    .text                                    0x080003b0   Section       36  init.o(.text)
    i.BusFault_Handler                       0x080003d4   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.DMA1_Channel3_IRQHandler               0x080003d8   Section        0  stm32f1xx_it.o(i.DMA1_Channel3_IRQHandler)
    i.DMA1_Channel5_IRQHandler               0x080003e4   Section        0  stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler)
    i.DMA_SetConfig                          0x080003f0   Section        0  stm32f1xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x080003f1   Thumb Code    44  stm32f1xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x0800041c   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x0800041e   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x08000422   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08000468   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08000500   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08000668   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x080006cc   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08000740   Section        0  stm32f1xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08000764   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_TogglePin                     0x08000954   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    i.HAL_GPIO_WritePin                      0x08000964   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08000974   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08000980   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08000990   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x080009b4   Section        0  stm32f1xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080009f8   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08000a34   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08000a50   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08000a90   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08000ab4   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08000c08   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08000c14   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08000c34   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08000c54   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08000ca0   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08001024   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x0800104c   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x0800104e   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08001050   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x080010b8   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08001104   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08001198   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_Base_Stop_IT                   0x080011fc   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT)
    i.HAL_TIM_ConfigClockSource              0x0800122c   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_IC_CaptureCallback             0x08001314   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08001316   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_OC_DelayElapsedCallback        0x0800144e   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08001450   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PeriodElapsedCallback          0x08001452   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08001454   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08001456   Section        0  stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x080014a8   Section        0  uart_app.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x08001518   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x0800151c   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x0800174c   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x080017b4   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x08001900   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08001902   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x08001904   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x080019b2   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x080019b4   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.Interpolation_Init                     0x080019b8   Section        0  servo_app.o(i.Interpolation_Init)
    i.Interpolation_Step                     0x080019c8   Section        0  servo_app.o(i.Interpolation_Step)
    i.Laser_ON                               0x08001acc   Section        0  laser_app.o(i.Laser_ON)
    i.MX_DMA_Init                            0x08001adc   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08001b18   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_TIM1_Init                           0x08001bc8   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x08001c34   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x08001c9c   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_USART1_UART_Init                    0x08001d08   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART3_UART_Init                    0x08001d58   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MemManage_Handler                      0x08001da8   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001daa   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.PID_Calculate                          0x08001dac   Section        0  pid.o(i.PID_Calculate)
    i.PID_Init                               0x08001e50   Section        0  pid.o(i.PID_Init)
    i.PendSV_Handler                         0x08001e6c   Section        0  stm32f1xx_it.o(i.PendSV_Handler)
    i.RCC_Delay                              0x08001e70   Section        0  stm32f1xx_hal_rcc.o(i.RCC_Delay)
    RCC_Delay                                0x08001e71   Thumb Code    30  stm32f1xx_hal_rcc.o(i.RCC_Delay)
    i.SVC_Handler                            0x08001e94   Section        0  stm32f1xx_it.o(i.SVC_Handler)
    i.Step_Motor_X_Cmd                       0x08001e98   Section        0  servo_app.o(i.Step_Motor_X_Cmd)
    i.Step_Motor_X_Dir                       0x08001ea8   Section        0  servo_app.o(i.Step_Motor_X_Dir)
    i.Step_Motor_Y_Cmd                       0x08001eb8   Section        0  servo_app.o(i.Step_Motor_Y_Cmd)
    i.Step_Motor_Y_Dir                       0x08001ec8   Section        0  servo_app.o(i.Step_Motor_Y_Dir)
    i.SysTick_Handler                        0x08001ed8   Section        0  stm32f1xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08001edc   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08001f3a   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.TIM1_UP_IRQHandler                     0x08001f3c   Section        0  stm32f1xx_it.o(i.TIM1_UP_IRQHandler)
    i.TIM2_IRQHandler                        0x08001ff0   Section        0  stm32f1xx_it.o(i.TIM2_IRQHandler)
    i.TIM3_IRQHandler                        0x080020a4   Section        0  stm32f1xx_it.o(i.TIM3_IRQHandler)
    i.TIM_Base_SetConfig                     0x080020cc   Section        0  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_ETR_SetConfig                      0x08002144   Section        0  stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08002158   Section        0  stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08002159   Thumb Code    16  stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08002168   Section        0  stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08002169   Thumb Code    34  stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x0800218a   Section        0  stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x0800218b   Thumb Code    36  stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.Tripod_Interpolation_Move              0x080021b0   Section        0  servo_app.o(i.Tripod_Interpolation_Move)
    i.Tripod_PID_Goto                        0x08002264   Section        0  servo_app.o(i.Tripod_PID_Goto)
    i.Tripod_PID_Interpolation_Line          0x08002344   Section        0  servo_app.o(i.Tripod_PID_Interpolation_Line)
    i.Tripod_PID_Task1                       0x080023ac   Section        0  servo_app.o(i.Tripod_PID_Task1)
    i.Tripod_PID_Text                        0x08002494   Section        0  servo_app.o(i.Tripod_PID_Text)
    i.Tripod_X_Abs_Step                      0x08002640   Section        0  servo_app.o(i.Tripod_X_Abs_Step)
    i.Tripod_X_Angle                         0x08002698   Section        0  servo_app.o(i.Tripod_X_Angle)
    i.Tripod_X_Step                          0x080026fc   Section        0  servo_app.o(i.Tripod_X_Step)
    i.Tripod_Y_Abs_Step                      0x08002744   Section        0  servo_app.o(i.Tripod_Y_Abs_Step)
    i.Tripod_Y_Angle                         0x0800279c   Section        0  servo_app.o(i.Tripod_Y_Angle)
    i.Tripod_Y_Step                          0x08002800   Section        0  servo_app.o(i.Tripod_Y_Step)
    i.UART_DMAAbortOnError                   0x08002848   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08002849   Thumb Code    16  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08002858   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08002859   Thumb Code    74  stm32f1xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x080028a2   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x080028a3   Thumb Code   134  stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08002928   Section        0  stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08002929   Thumb Code    30  stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08002946   Section        0  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08002947   Thumb Code    78  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08002994   Section        0  stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08002995   Thumb Code    26  stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_EndTxTransfer                     0x080029ae   Section        0  stm32f1xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x080029af   Thumb Code    28  stm32f1xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x080029ca   Section        0  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x080029cb   Thumb Code   200  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08002a94   Section        0  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08002a95   Thumb Code   178  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08002b4c   Section        0  stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Transmit_IT                       0x08002be0   Section        0  stm32f1xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x08002be1   Thumb Code    94  stm32f1xx_hal_uart.o(i.UART_Transmit_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08002c3e   Section        0  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08002c3f   Thumb Code   116  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08002cb4   Section        0  stm32f1xx_it.o(i.USART1_IRQHandler)
    i.USART3_IRQHandler                      0x08002ce0   Section        0  stm32f1xx_it.o(i.USART3_IRQHandler)
    i.UsageFault_Handler                     0x08002d0c   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.__0printf$1                            0x08002d10   Section        0  printf1.o(i.__0printf$1)
    i.__NVIC_SetPriority                     0x08002d30   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08002d31   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x08002d50   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08002d5e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08002d60   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x08002d70   Section        0  printf1.o(i._printf_core)
    _printf_core                             0x08002d71   Thumb Code   336  printf1.o(i._printf_core)
    i.fputc                                  0x08002ec4   Section        0  usart.o(i.fputc)
    i.main                                   0x08002edc   Section        0  main.o(i.main)
    i.servo_system_init                      0x08002f84   Section        0  bsp_system.o(i.servo_system_init)
    i.task_run                               0x08002fa0   Section        0  task.o(i.task_run)
    i.uart3_task                             0x08002fd4   Section        0  uart_app.o(i.uart3_task)
    i.uart_task                              0x080031cc   Section        0  uart_app.o(i.uart_task)
    .constdata                               0x08003424   Section       18  stm32f1xx_hal_rcc.o(.constdata)
    aPredivFactorTable                       0x08003424   Data           2  stm32f1xx_hal_rcc.o(.constdata)
    aPLLMULFactorTable                       0x08003426   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    .constdata                               0x08003436   Section       16  system_stm32f1xx.o(.constdata)
    .constdata                               0x08003446   Section        8  system_stm32f1xx.o(.constdata)
    .data                                    0x20000000   Section       12  stm32f1xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x20000010   Section       10  servo_app.o(.data)
    PID_text_index                           0x20000011   Data           1  servo_app.o(.data)
    current_point                            0x20000012   Data           1  servo_app.o(.data)
    stable_count                             0x20000014   Data           2  servo_app.o(.data)
    .data                                    0x2000001a   Section       12  uart_app.o(.data)
    .data                                    0x20000028   Section       24  task.o(.data)
    task_list                                0x20000028   Data          24  task.o(.data)
    .data                                    0x20000040   Section        4  stdout.o(.data)
    .bss                                     0x20000044   Section      216  tim.o(.bss)
    .bss                                     0x2000011c   Section      280  usart.o(.bss)
    .bss                                     0x20000234   Section       32  stm32f1xx_it.o(.bss)
    .bss                                     0x20000254   Section       42  servo_app.o(.bss)
    .bss                                     0x2000027e   Section      532  uart_app.o(.bss)
    .bss                                     0x20000494   Section       40  pid.o(.bss)
    .bss                                     0x200004bc   Section       40  pid.o(.bss)
    STACK                                    0x200004e8   Section     1024  startup_stm32f103xb.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f103xb.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xb.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f103xb.o(RESET)
    __main                                   0x080000ed   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080000ed   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080000f1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080000f5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080000f5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080000f5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080000f5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080000fd   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x08000101   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x08000101   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x08000105   Thumb Code     8  startup_stm32f103xb.o(.text)
    ADC1_2_IRQHandler                        0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_RX1_IRQHandler                      0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_SCE_IRQHandler                      0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI0_IRQHandler                         0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI15_10_IRQHandler                     0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI1_IRQHandler                         0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI2_IRQHandler                         0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI3_IRQHandler                         0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI4_IRQHandler                         0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI9_5_IRQHandler                       0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    FLASH_IRQHandler                         0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_ER_IRQHandler                       0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_EV_IRQHandler                       0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_ER_IRQHandler                       0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_EV_IRQHandler                       0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    PVD_IRQHandler                           0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    RCC_IRQHandler                           0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_Alarm_IRQHandler                     0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_IRQHandler                           0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI1_IRQHandler                          0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI2_IRQHandler                          0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TAMPER_IRQHandler                        0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_BRK_IRQHandler                      0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_CC_IRQHandler                       0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM4_IRQHandler                          0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART2_IRQHandler                        0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    USBWakeUp_IRQHandler                     0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    WWDG_IRQHandler                          0x0800011f   Thumb Code     0  startup_stm32f103xb.o(.text)
    __aeabi_memset                           0x08000129   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000129   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000129   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000137   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000137   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000137   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800013b   Thumb Code    18  memseta.o(.text)
    strncmp                                  0x0800014d   Thumb Code    30  strncmp.o(.text)
    __aeabi_fadd                             0x0800016b   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x0800020f   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x08000215   Thumb Code     6  fadd.o(.text)
    __aeabi_fmul                             0x0800021b   Thumb Code   100  fmul.o(.text)
    __aeabi_i2f                              0x0800027f   Thumb Code    18  fflti.o(.text)
    __aeabi_f2iz                             0x08000291   Thumb Code    50  ffixi.o(.text)
    __aeabi_f2uiz                            0x080002c3   Thumb Code    40  ffixui.o(.text)
    __aeabi_cfcmpeq                          0x080002ed   Thumb Code     0  cfcmple.o(.text)
    __aeabi_cfcmple                          0x080002ed   Thumb Code    20  cfcmple.o(.text)
    __aeabi_cfrcmple                         0x08000301   Thumb Code    20  cfrcmple.o(.text)
    __aeabi_uidiv                            0x08000315   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000315   Thumb Code    44  uidiv.o(.text)
    __I$use$fp                               0x08000341   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08000341   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08000353   Thumb Code    92  fepilogue.o(.text)
    __scatterload                            0x080003b1   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080003b1   Thumb Code     0  init.o(.text)
    BusFault_Handler                         0x080003d5   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    DMA1_Channel3_IRQHandler                 0x080003d9   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel3_IRQHandler)
    DMA1_Channel5_IRQHandler                 0x080003e5   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler)
    DebugMon_Handler                         0x0800041d   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x0800041f   Thumb Code     4  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x08000423   Thumb Code    70  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08000469   Thumb Code   148  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08000501   Thumb Code   336  stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08000669   Thumb Code    90  stm32f1xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x080006cd   Thumb Code   116  stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08000741   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08000765   Thumb Code   464  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_TogglePin                       0x08000955   Thumb Code    16  stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x08000965   Thumb Code    14  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08000975   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08000981   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08000991   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x080009b5   Thumb Code    58  stm32f1xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x080009f9   Thumb Code    52  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08000a35   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08000a51   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08000a91   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08000ab5   Thumb Code   318  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08000c09   Thumb Code     6  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08000c15   Thumb Code    22  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08000c35   Thumb Code    22  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08000c55   Thumb Code    58  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08000ca1   Thumb Code   882  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08001025   Thumb Code    40  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x0800104d   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x0800104f   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_MasterConfigSynchronization    0x08001051   Thumb Code    90  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x080010b9   Thumb Code    74  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08001105   Thumb Code   134  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08001199   Thumb Code    86  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_Base_Stop_IT                     0x080011fd   Thumb Code    48  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT)
    HAL_TIM_ConfigClockSource                0x0800122d   Thumb Code   232  stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_IC_CaptureCallback               0x08001315   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08001317   Thumb Code   312  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x0800144f   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_PulseFinishedCallback        0x08001451   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PeriodElapsedCallback            0x08001453   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08001455   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08001457   Thumb Code    82  stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x080014a9   Thumb Code    96  uart_app.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08001519   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x0800151d   Thumb Code   554  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x0800174d   Thumb Code   102  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x080017b5   Thumb Code   302  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08001901   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08001903   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08001905   Thumb Code   174  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x080019b3   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x080019b5   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    Interpolation_Init                       0x080019b9   Thumb Code    12  servo_app.o(i.Interpolation_Init)
    Interpolation_Step                       0x080019c9   Thumb Code   240  servo_app.o(i.Interpolation_Step)
    Laser_ON                                 0x08001acd   Thumb Code    10  laser_app.o(i.Laser_ON)
    MX_DMA_Init                              0x08001add   Thumb Code    56  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08001b19   Thumb Code   162  gpio.o(i.MX_GPIO_Init)
    MX_TIM1_Init                             0x08001bc9   Thumb Code    98  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x08001c35   Thumb Code    98  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x08001c9d   Thumb Code    98  tim.o(i.MX_TIM3_Init)
    MX_USART1_UART_Init                      0x08001d09   Thumb Code    66  usart.o(i.MX_USART1_UART_Init)
    MX_USART3_UART_Init                      0x08001d59   Thumb Code    66  usart.o(i.MX_USART3_UART_Init)
    MemManage_Handler                        0x08001da9   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08001dab   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    PID_Calculate                            0x08001dad   Thumb Code   164  pid.o(i.PID_Calculate)
    PID_Init                                 0x08001e51   Thumb Code    28  pid.o(i.PID_Init)
    PendSV_Handler                           0x08001e6d   Thumb Code     2  stm32f1xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08001e95   Thumb Code     2  stm32f1xx_it.o(i.SVC_Handler)
    Step_Motor_X_Cmd                         0x08001e99   Thumb Code    10  servo_app.o(i.Step_Motor_X_Cmd)
    Step_Motor_X_Dir                         0x08001ea9   Thumb Code    10  servo_app.o(i.Step_Motor_X_Dir)
    Step_Motor_Y_Cmd                         0x08001eb9   Thumb Code    10  servo_app.o(i.Step_Motor_Y_Cmd)
    Step_Motor_Y_Dir                         0x08001ec9   Thumb Code    10  servo_app.o(i.Step_Motor_Y_Dir)
    SysTick_Handler                          0x08001ed9   Thumb Code     4  stm32f1xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08001edd   Thumb Code    94  main.o(i.SystemClock_Config)
    SystemInit                               0x08001f3b   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    TIM1_UP_IRQHandler                       0x08001f3d   Thumb Code   166  stm32f1xx_it.o(i.TIM1_UP_IRQHandler)
    TIM2_IRQHandler                          0x08001ff1   Thumb Code   166  stm32f1xx_it.o(i.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x080020a5   Thumb Code    30  stm32f1xx_it.o(i.TIM3_IRQHandler)
    TIM_Base_SetConfig                       0x080020cd   Thumb Code   108  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x08002145   Thumb Code    20  stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig)
    Tripod_Interpolation_Move                0x080021b1   Thumb Code   160  servo_app.o(i.Tripod_Interpolation_Move)
    Tripod_PID_Goto                          0x08002265   Thumb Code   194  servo_app.o(i.Tripod_PID_Goto)
    Tripod_PID_Interpolation_Line            0x08002345   Thumb Code    88  servo_app.o(i.Tripod_PID_Interpolation_Line)
    Tripod_PID_Task1                         0x080023ad   Thumb Code   204  servo_app.o(i.Tripod_PID_Task1)
    Tripod_PID_Text                          0x08002495   Thumb Code   362  servo_app.o(i.Tripod_PID_Text)
    Tripod_X_Abs_Step                        0x08002641   Thumb Code    84  servo_app.o(i.Tripod_X_Abs_Step)
    Tripod_X_Angle                           0x08002699   Thumb Code    90  servo_app.o(i.Tripod_X_Angle)
    Tripod_X_Step                            0x080026fd   Thumb Code    60  servo_app.o(i.Tripod_X_Step)
    Tripod_Y_Abs_Step                        0x08002745   Thumb Code    84  servo_app.o(i.Tripod_Y_Abs_Step)
    Tripod_Y_Angle                           0x0800279d   Thumb Code    90  servo_app.o(i.Tripod_Y_Angle)
    Tripod_Y_Step                            0x08002801   Thumb Code    60  servo_app.o(i.Tripod_Y_Step)
    UART_Start_Receive_DMA                   0x08002b4d   Thumb Code   134  stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
    USART1_IRQHandler                        0x08002cb5   Thumb Code    32  stm32f1xx_it.o(i.USART1_IRQHandler)
    USART3_IRQHandler                        0x08002ce1   Thumb Code    32  stm32f1xx_it.o(i.USART3_IRQHandler)
    UsageFault_Handler                       0x08002d0d   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    __0printf$1                              0x08002d11   Thumb Code    22  printf1.o(i.__0printf$1)
    __1printf$1                              0x08002d11   Thumb Code     0  printf1.o(i.__0printf$1)
    __2printf                                0x08002d11   Thumb Code     0  printf1.o(i.__0printf$1)
    __scatterload_copy                       0x08002d51   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08002d5f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08002d61   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    fputc                                    0x08002ec5   Thumb Code    20  usart.o(i.fputc)
    main                                     0x08002edd   Thumb Code   132  main.o(i.main)
    servo_system_init                        0x08002f85   Thumb Code    26  bsp_system.o(i.servo_system_init)
    task_run                                 0x08002fa1   Thumb Code    46  task.o(i.task_run)
    uart3_task                               0x08002fd5   Thumb Code   482  uart_app.o(i.uart3_task)
    uart_task                                0x080031cd   Thumb Code   410  uart_app.o(i.uart_task)
    AHBPrescTable                            0x08003436   Data          16  system_stm32f1xx.o(.constdata)
    APBPrescTable                            0x08003446   Data           8  system_stm32f1xx.o(.constdata)
    Region$$Table$$Base                      0x08003450   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003470   Number         0  anon$$obj.o(Region$$Table)
    uwTickFreq                               0x20000000   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32f1xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f1xx.o(.data)
    step_Error_Flag                          0x20000010   Data           1  servo_app.o(.data)
    current_x                                0x20000016   Data           2  servo_app.o(.data)
    current_y                                0x20000018   Data           2  servo_app.o(.data)
    uart1_rx_flag                            0x2000001a   Data           1  uart_app.o(.data)
    uart3_rx_flag                            0x2000001b   Data           1  uart_app.o(.data)
    i                                        0x2000001c   Data           1  uart_app.o(.data)
    Task1_Start                              0x2000001d   Data           1  uart_app.o(.data)
    Task2_Start                              0x2000001e   Data           1  uart_app.o(.data)
    Task3_Start                              0x2000001f   Data           1  uart_app.o(.data)
    Task_Stop_Plas                           0x20000020   Data           1  uart_app.o(.data)
    Task_Stop_num                            0x20000021   Data           1  uart_app.o(.data)
    X_Step                                   0x20000022   Data           2  uart_app.o(.data)
    Y_Step                                   0x20000024   Data           2  uart_app.o(.data)
    __stdout                                 0x20000040   Data           4  stdout.o(.data)
    htim1                                    0x20000044   Data          72  tim.o(.bss)
    htim2                                    0x2000008c   Data          72  tim.o(.bss)
    htim3                                    0x200000d4   Data          72  tim.o(.bss)
    huart1                                   0x2000011c   Data          72  usart.o(.bss)
    huart3                                   0x20000164   Data          72  usart.o(.bss)
    hdma_usart1_rx                           0x200001ac   Data          68  usart.o(.bss)
    hdma_usart3_rx                           0x200001f0   Data          68  usart.o(.bss)
    Tripod_X                                 0x20000234   Data          16  stm32f1xx_it.o(.bss)
    Tripod_Y                                 0x20000244   Data          16  stm32f1xx_it.o(.bss)
    interp                                   0x20000254   Data          26  servo_app.o(.bss)
    PID_text                                 0x2000026e   Data          16  servo_app.o(.bss)
    Task1_step                               0x2000027e   Data          20  uart_app.o(.bss)
    uart1_rx_dma_buf                         0x20000292   Data         128  uart_app.o(.bss)
    uart3_rx_dma_buf                         0x20000312   Data         128  uart_app.o(.bss)
    uart1_rx_buf                             0x20000392   Data         128  uart_app.o(.bss)
    uart3_rx_buf                             0x20000412   Data         128  uart_app.o(.bss)
    pid_x                                    0x20000494   Data          40  pid.o(.bss)
    pid_y                                    0x200004bc   Data          40  pid.o(.bss)
    __initial_sp                             0x200008e8   Data           0  startup_stm32f103xb.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000034b4, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003470, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f103xb.o
    0x080000ec   0x080000ec   0x00000000   Code   RO         3262  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080000ec   0x080000ec   0x00000004   Code   RO         3546    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080000f0   0x080000f0   0x00000004   Code   RO         3549    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         3551    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         3553    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080000f4   0x080000f4   0x00000008   Code   RO         3554    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080000fc   0x080000fc   0x00000004   Code   RO         3561    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x08000100   0x08000100   0x00000000   Code   RO         3556    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x08000100   0x08000100   0x00000000   Code   RO         3558    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x08000100   0x08000100   0x00000004   Code   RO         3547    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000104   0x08000104   0x00000024   Code   RO            4    .text               startup_stm32f103xb.o
    0x08000128   0x08000128   0x00000024   Code   RO         3267    .text               mc_w.l(memseta.o)
    0x0800014c   0x0800014c   0x0000001e   Code   RO         3269    .text               mc_w.l(strncmp.o)
    0x0800016a   0x0800016a   0x000000b0   Code   RO         3532    .text               mf_w.l(fadd.o)
    0x0800021a   0x0800021a   0x00000064   Code   RO         3534    .text               mf_w.l(fmul.o)
    0x0800027e   0x0800027e   0x00000012   Code   RO         3536    .text               mf_w.l(fflti.o)
    0x08000290   0x08000290   0x00000032   Code   RO         3538    .text               mf_w.l(ffixi.o)
    0x080002c2   0x080002c2   0x00000028   Code   RO         3540    .text               mf_w.l(ffixui.o)
    0x080002ea   0x080002ea   0x00000002   PAD
    0x080002ec   0x080002ec   0x00000014   Code   RO         3542    .text               mf_w.l(cfcmple.o)
    0x08000300   0x08000300   0x00000014   Code   RO         3544    .text               mf_w.l(cfrcmple.o)
    0x08000314   0x08000314   0x0000002c   Code   RO         3563    .text               mc_w.l(uidiv.o)
    0x08000340   0x08000340   0x00000000   Code   RO         3567    .text               mc_w.l(iusefp.o)
    0x08000340   0x08000340   0x0000006e   Code   RO         3568    .text               mf_w.l(fepilogue.o)
    0x080003ae   0x080003ae   0x00000002   PAD
    0x080003b0   0x080003b0   0x00000024   Code   RO         3580    .text               mc_w.l(init.o)
    0x080003d4   0x080003d4   0x00000002   Code   RO          344    i.BusFault_Handler  stm32f1xx_it.o
    0x080003d6   0x080003d6   0x00000002   PAD
    0x080003d8   0x080003d8   0x0000000c   Code   RO          345    i.DMA1_Channel3_IRQHandler  stm32f1xx_it.o
    0x080003e4   0x080003e4   0x0000000c   Code   RO          346    i.DMA1_Channel5_IRQHandler  stm32f1xx_it.o
    0x080003f0   0x080003f0   0x0000002c   Code   RO         1898    i.DMA_SetConfig     stm32f1xx_hal_dma.o
    0x0800041c   0x0800041c   0x00000002   Code   RO          347    i.DebugMon_Handler  stm32f1xx_it.o
    0x0800041e   0x0800041e   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08000422   0x08000422   0x00000046   Code   RO         1899    i.HAL_DMA_Abort     stm32f1xx_hal_dma.o
    0x08000468   0x08000468   0x00000098   Code   RO         1900    i.HAL_DMA_Abort_IT  stm32f1xx_hal_dma.o
    0x08000500   0x08000500   0x00000168   Code   RO         1904    i.HAL_DMA_IRQHandler  stm32f1xx_hal_dma.o
    0x08000668   0x08000668   0x00000064   Code   RO         1905    i.HAL_DMA_Init      stm32f1xx_hal_dma.o
    0x080006cc   0x080006cc   0x00000074   Code   RO         1909    i.HAL_DMA_Start_IT  stm32f1xx_hal_dma.o
    0x08000740   0x08000740   0x00000024   Code   RO         1520    i.HAL_Delay         stm32f1xx_hal.o
    0x08000764   0x08000764   0x000001f0   Code   RO         1835    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x08000954   0x08000954   0x00000010   Code   RO         1838    i.HAL_GPIO_TogglePin  stm32f1xx_hal_gpio.o
    0x08000964   0x08000964   0x0000000e   Code   RO         1839    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x08000972   0x08000972   0x00000002   PAD
    0x08000974   0x08000974   0x0000000c   Code   RO         1524    i.HAL_GetTick       stm32f1xx_hal.o
    0x08000980   0x08000980   0x00000010   Code   RO         1530    i.HAL_IncTick       stm32f1xx_hal.o
    0x08000990   0x08000990   0x00000024   Code   RO         1531    i.HAL_Init          stm32f1xx_hal.o
    0x080009b4   0x080009b4   0x00000044   Code   RO         1532    i.HAL_InitTick      stm32f1xx_hal.o
    0x080009f8   0x080009f8   0x0000003c   Code   RO          469    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x08000a34   0x08000a34   0x0000001a   Code   RO         1995    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x08000a4e   0x08000a4e   0x00000002   PAD
    0x08000a50   0x08000a50   0x00000040   Code   RO         2001    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08000a90   0x08000a90   0x00000024   Code   RO         2002    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08000ab4   0x08000ab4   0x00000154   Code   RO         1688    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08000c08   0x08000c08   0x0000000c   Code   RO         1693    i.HAL_RCC_GetHCLKFreq  stm32f1xx_hal_rcc.o
    0x08000c14   0x08000c14   0x00000020   Code   RO         1695    i.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x08000c34   0x08000c34   0x00000020   Code   RO         1696    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x08000c54   0x08000c54   0x0000004c   Code   RO         1697    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x08000ca0   0x08000ca0   0x00000384   Code   RO         1700    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x08001024   0x08001024   0x00000028   Code   RO         2006    i.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x0800104c   0x0800104c   0x00000002   Code   RO         1246    i.HAL_TIMEx_BreakCallback  stm32f1xx_hal_tim_ex.o
    0x0800104e   0x0800104e   0x00000002   Code   RO         1247    i.HAL_TIMEx_CommutCallback  stm32f1xx_hal_tim_ex.o
    0x08001050   0x08001050   0x00000068   Code   RO         1265    i.HAL_TIMEx_MasterConfigSynchronization  stm32f1xx_hal_tim_ex.o
    0x080010b8   0x080010b8   0x0000004a   Code   RO          531    i.HAL_TIM_Base_Init  stm32f1xx_hal_tim.o
    0x08001102   0x08001102   0x00000002   PAD
    0x08001104   0x08001104   0x00000094   Code   RO          237    i.HAL_TIM_Base_MspInit  tim.o
    0x08001198   0x08001198   0x00000064   Code   RO          536    i.HAL_TIM_Base_Start_IT  stm32f1xx_hal_tim.o
    0x080011fc   0x080011fc   0x00000030   Code   RO          539    i.HAL_TIM_Base_Stop_IT  stm32f1xx_hal_tim.o
    0x0800122c   0x0800122c   0x000000e8   Code   RO          540    i.HAL_TIM_ConfigClockSource  stm32f1xx_hal_tim.o
    0x08001314   0x08001314   0x00000002   Code   RO          565    i.HAL_TIM_IC_CaptureCallback  stm32f1xx_hal_tim.o
    0x08001316   0x08001316   0x00000138   Code   RO          579    i.HAL_TIM_IRQHandler  stm32f1xx_hal_tim.o
    0x0800144e   0x0800144e   0x00000002   Code   RO          582    i.HAL_TIM_OC_DelayElapsedCallback  stm32f1xx_hal_tim.o
    0x08001450   0x08001450   0x00000002   Code   RO          609    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f1xx_hal_tim.o
    0x08001452   0x08001452   0x00000002   Code   RO          617    i.HAL_TIM_PeriodElapsedCallback  stm32f1xx_hal_tim.o
    0x08001454   0x08001454   0x00000002   Code   RO          622    i.HAL_TIM_TriggerCallback  stm32f1xx_hal_tim.o
    0x08001456   0x08001456   0x00000052   Code   RO         2526    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f1xx_hal_uart.o
    0x080014a8   0x080014a8   0x00000070   Code   RO         3120    i.HAL_UARTEx_RxEventCallback  uart_app.o
    0x08001518   0x08001518   0x00000002   Code   RO         2542    i.HAL_UART_ErrorCallback  stm32f1xx_hal_uart.o
    0x0800151a   0x0800151a   0x00000002   PAD
    0x0800151c   0x0800151c   0x00000230   Code   RO         2545    i.HAL_UART_IRQHandler  stm32f1xx_hal_uart.o
    0x0800174c   0x0800174c   0x00000066   Code   RO         2546    i.HAL_UART_Init     stm32f1xx_hal_uart.o
    0x080017b2   0x080017b2   0x00000002   PAD
    0x080017b4   0x080017b4   0x0000014c   Code   RO          291    i.HAL_UART_MspInit  usart.o
    0x08001900   0x08001900   0x00000002   Code   RO         2552    i.HAL_UART_RxCpltCallback  stm32f1xx_hal_uart.o
    0x08001902   0x08001902   0x00000002   Code   RO         2553    i.HAL_UART_RxHalfCpltCallback  stm32f1xx_hal_uart.o
    0x08001904   0x08001904   0x000000ae   Code   RO         2554    i.HAL_UART_Transmit  stm32f1xx_hal_uart.o
    0x080019b2   0x080019b2   0x00000002   Code   RO         2557    i.HAL_UART_TxCpltCallback  stm32f1xx_hal_uart.o
    0x080019b4   0x080019b4   0x00000002   Code   RO          348    i.HardFault_Handler  stm32f1xx_it.o
    0x080019b6   0x080019b6   0x00000002   PAD
    0x080019b8   0x080019b8   0x00000010   Code   RO         2927    i.Interpolation_Init  servo_app.o
    0x080019c8   0x080019c8   0x00000104   Code   RO         2928    i.Interpolation_Step  servo_app.o
    0x08001acc   0x08001acc   0x00000010   Code   RO         3088    i.Laser_ON          laser_app.o
    0x08001adc   0x08001adc   0x0000003c   Code   RO          212    i.MX_DMA_Init       dma.o
    0x08001b18   0x08001b18   0x000000b0   Code   RO          188    i.MX_GPIO_Init      gpio.o
    0x08001bc8   0x08001bc8   0x0000006c   Code   RO          238    i.MX_TIM1_Init      tim.o
    0x08001c34   0x08001c34   0x00000068   Code   RO          239    i.MX_TIM2_Init      tim.o
    0x08001c9c   0x08001c9c   0x0000006c   Code   RO          240    i.MX_TIM3_Init      tim.o
    0x08001d08   0x08001d08   0x00000050   Code   RO          292    i.MX_USART1_UART_Init  usart.o
    0x08001d58   0x08001d58   0x00000050   Code   RO          293    i.MX_USART3_UART_Init  usart.o
    0x08001da8   0x08001da8   0x00000002   Code   RO          349    i.MemManage_Handler  stm32f1xx_it.o
    0x08001daa   0x08001daa   0x00000002   Code   RO          350    i.NMI_Handler       stm32f1xx_it.o
    0x08001dac   0x08001dac   0x000000a4   Code   RO         3167    i.PID_Calculate     pid.o
    0x08001e50   0x08001e50   0x0000001c   Code   RO         3168    i.PID_Init          pid.o
    0x08001e6c   0x08001e6c   0x00000002   Code   RO          351    i.PendSV_Handler    stm32f1xx_it.o
    0x08001e6e   0x08001e6e   0x00000002   PAD
    0x08001e70   0x08001e70   0x00000024   Code   RO         1701    i.RCC_Delay         stm32f1xx_hal_rcc.o
    0x08001e94   0x08001e94   0x00000002   Code   RO          352    i.SVC_Handler       stm32f1xx_it.o
    0x08001e96   0x08001e96   0x00000002   PAD
    0x08001e98   0x08001e98   0x00000010   Code   RO         2929    i.Step_Motor_X_Cmd  servo_app.o
    0x08001ea8   0x08001ea8   0x00000010   Code   RO         2930    i.Step_Motor_X_Dir  servo_app.o
    0x08001eb8   0x08001eb8   0x00000010   Code   RO         2932    i.Step_Motor_Y_Cmd  servo_app.o
    0x08001ec8   0x08001ec8   0x00000010   Code   RO         2933    i.Step_Motor_Y_Dir  servo_app.o
    0x08001ed8   0x08001ed8   0x00000004   Code   RO          353    i.SysTick_Handler   stm32f1xx_it.o
    0x08001edc   0x08001edc   0x0000005e   Code   RO           14    i.SystemClock_Config  main.o
    0x08001f3a   0x08001f3a   0x00000002   Code   RO         2890    i.SystemInit        system_stm32f1xx.o
    0x08001f3c   0x08001f3c   0x000000b4   Code   RO          354    i.TIM1_UP_IRQHandler  stm32f1xx_it.o
    0x08001ff0   0x08001ff0   0x000000b4   Code   RO          355    i.TIM2_IRQHandler   stm32f1xx_it.o
    0x080020a4   0x080020a4   0x00000028   Code   RO          356    i.TIM3_IRQHandler   stm32f1xx_it.o
    0x080020cc   0x080020cc   0x00000078   Code   RO          624    i.TIM_Base_SetConfig  stm32f1xx_hal_tim.o
    0x08002144   0x08002144   0x00000014   Code   RO          635    i.TIM_ETR_SetConfig  stm32f1xx_hal_tim.o
    0x08002158   0x08002158   0x00000010   Code   RO          636    i.TIM_ITRx_SetConfig  stm32f1xx_hal_tim.o
    0x08002168   0x08002168   0x00000022   Code   RO          642    i.TIM_TI1_ConfigInputStage  stm32f1xx_hal_tim.o
    0x0800218a   0x0800218a   0x00000024   Code   RO          644    i.TIM_TI2_ConfigInputStage  stm32f1xx_hal_tim.o
    0x080021ae   0x080021ae   0x00000002   PAD
    0x080021b0   0x080021b0   0x000000b4   Code   RO         2935    i.Tripod_Interpolation_Move  servo_app.o
    0x08002264   0x08002264   0x000000e0   Code   RO         2936    i.Tripod_PID_Goto   servo_app.o
    0x08002344   0x08002344   0x00000068   Code   RO         2937    i.Tripod_PID_Interpolation_Line  servo_app.o
    0x080023ac   0x080023ac   0x000000e8   Code   RO         2938    i.Tripod_PID_Task1  servo_app.o
    0x08002494   0x08002494   0x000001ac   Code   RO         2939    i.Tripod_PID_Text   servo_app.o
    0x08002640   0x08002640   0x00000058   Code   RO         2940    i.Tripod_X_Abs_Step  servo_app.o
    0x08002698   0x08002698   0x00000064   Code   RO         2941    i.Tripod_X_Angle    servo_app.o
    0x080026fc   0x080026fc   0x00000048   Code   RO         2943    i.Tripod_X_Step     servo_app.o
    0x08002744   0x08002744   0x00000058   Code   RO         2944    i.Tripod_Y_Abs_Step  servo_app.o
    0x0800279c   0x0800279c   0x00000064   Code   RO         2945    i.Tripod_Y_Angle    servo_app.o
    0x08002800   0x08002800   0x00000048   Code   RO         2947    i.Tripod_Y_Step     servo_app.o
    0x08002848   0x08002848   0x00000010   Code   RO         2559    i.UART_DMAAbortOnError  stm32f1xx_hal_uart.o
    0x08002858   0x08002858   0x0000004a   Code   RO         2560    i.UART_DMAError     stm32f1xx_hal_uart.o
    0x080028a2   0x080028a2   0x00000086   Code   RO         2561    i.UART_DMAReceiveCplt  stm32f1xx_hal_uart.o
    0x08002928   0x08002928   0x0000001e   Code   RO         2563    i.UART_DMARxHalfCplt  stm32f1xx_hal_uart.o
    0x08002946   0x08002946   0x0000004e   Code   RO         2569    i.UART_EndRxTransfer  stm32f1xx_hal_uart.o
    0x08002994   0x08002994   0x0000001a   Code   RO         2570    i.UART_EndTransmit_IT  stm32f1xx_hal_uart.o
    0x080029ae   0x080029ae   0x0000001c   Code   RO         2571    i.UART_EndTxTransfer  stm32f1xx_hal_uart.o
    0x080029ca   0x080029ca   0x000000c8   Code   RO         2572    i.UART_Receive_IT   stm32f1xx_hal_uart.o
    0x08002a92   0x08002a92   0x00000002   PAD
    0x08002a94   0x08002a94   0x000000b8   Code   RO         2573    i.UART_SetConfig    stm32f1xx_hal_uart.o
    0x08002b4c   0x08002b4c   0x00000094   Code   RO         2574    i.UART_Start_Receive_DMA  stm32f1xx_hal_uart.o
    0x08002be0   0x08002be0   0x0000005e   Code   RO         2576    i.UART_Transmit_IT  stm32f1xx_hal_uart.o
    0x08002c3e   0x08002c3e   0x00000074   Code   RO         2577    i.UART_WaitOnFlagUntilTimeout  stm32f1xx_hal_uart.o
    0x08002cb2   0x08002cb2   0x00000002   PAD
    0x08002cb4   0x08002cb4   0x0000002c   Code   RO          357    i.USART1_IRQHandler  stm32f1xx_it.o
    0x08002ce0   0x08002ce0   0x0000002c   Code   RO          358    i.USART3_IRQHandler  stm32f1xx_it.o
    0x08002d0c   0x08002d0c   0x00000002   Code   RO          359    i.UsageFault_Handler  stm32f1xx_it.o
    0x08002d0e   0x08002d0e   0x00000002   PAD
    0x08002d10   0x08002d10   0x00000020   Code   RO         3316    i.__0printf$1       mc_w.l(printf1.o)
    0x08002d30   0x08002d30   0x00000020   Code   RO         2008    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08002d50   0x08002d50   0x0000000e   Code   RO         3590    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08002d5e   0x08002d5e   0x00000002   Code   RO         3591    i.__scatterload_null  mc_w.l(handlers.o)
    0x08002d60   0x08002d60   0x0000000e   Code   RO         3592    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08002d6e   0x08002d6e   0x00000002   PAD
    0x08002d70   0x08002d70   0x00000154   Code   RO         3323    i._printf_core      mc_w.l(printf1.o)
    0x08002ec4   0x08002ec4   0x00000018   Code   RO          294    i.fputc             usart.o
    0x08002edc   0x08002edc   0x000000a8   Code   RO           15    i.main              main.o
    0x08002f84   0x08002f84   0x0000001a   Code   RO         3241    i.servo_system_init  bsp_system.o
    0x08002f9e   0x08002f9e   0x00000002   PAD
    0x08002fa0   0x08002fa0   0x00000034   Code   RO         3204    i.task_run          task.o
    0x08002fd4   0x08002fd4   0x000001f8   Code   RO         3121    i.uart3_task        uart_app.o
    0x080031cc   0x080031cc   0x00000258   Code   RO         3122    i.uart_task         uart_app.o
    0x08003424   0x08003424   0x00000012   Data   RO         1702    .constdata          stm32f1xx_hal_rcc.o
    0x08003436   0x08003436   0x00000010   Data   RO         2891    .constdata          system_stm32f1xx.o
    0x08003446   0x08003446   0x00000008   Data   RO         2892    .constdata          system_stm32f1xx.o
    0x0800344e   0x0800344e   0x00000002   PAD
    0x08003450   0x08003450   0x00000020   Data   RO         3588    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003470, Size: 0x000008e8, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08003470   0x0000000c   Data   RW         1538    .data               stm32f1xx_hal.o
    0x2000000c   0x0800347c   0x00000004   Data   RW         2893    .data               system_stm32f1xx.o
    0x20000010   0x08003480   0x0000000a   Data   RW         2949    .data               servo_app.o
    0x2000001a   0x0800348a   0x0000000c   Data   RW         3124    .data               uart_app.o
    0x20000026   0x08003496   0x00000002   PAD
    0x20000028   0x08003498   0x00000018   Data   RW         3205    .data               task.o
    0x20000040   0x080034b0   0x00000004   Data   RW         3562    .data               mc_w.l(stdout.o)
    0x20000044        -       0x000000d8   Zero   RW          241    .bss                tim.o
    0x2000011c        -       0x00000118   Zero   RW          295    .bss                usart.o
    0x20000234        -       0x00000020   Zero   RW          360    .bss                stm32f1xx_it.o
    0x20000254        -       0x0000002a   Zero   RW         2948    .bss                servo_app.o
    0x2000027e        -       0x00000214   Zero   RW         3123    .bss                uart_app.o
    0x20000492   0x080034b4   0x00000002   PAD
    0x20000494        -       0x00000028   Zero   RW         3169    .bss                pid.o
    0x200004bc        -       0x00000028   Zero   RW         3170    .bss                pid.o
    0x200004e4   0x080034b4   0x00000004   PAD
    0x200004e8        -       0x00000400   Zero   RW            1    STACK               startup_stm32f103xb.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        26          0          0          0          0        444   bsp_system.o
        60          4          0          0          0        698   dma.o
       176         14          0          0          0        871   gpio.o
        16          6          0          0          0        379   laser_app.o
       266         36          0          0          0     428648   main.o
       192          0          0          0         80       2173   pid.o
      2028        260          0         10         42      11973   servo_app.o
        36          8        236          0       1024        768   startup_stm32f103xb.o
       168         28          0         12          0       5649   stm32f1xx_hal.o
       198         14          0          0          0      28859   stm32f1xx_hal_cortex.o
       842         38          0          0          0       4822   stm32f1xx_hal_dma.o
       526         32          0          0          0       3289   stm32f1xx_hal_gpio.o
        60          8          0          0          0        826   stm32f1xx_hal_msp.o
      1428         90         18          0          0       6344   stm32f1xx_hal_rcc.o
      1002         26          0          0          0      10206   stm32f1xx_hal_tim.o
       108         14          0          0          0       2325   stm32f1xx_hal_tim_ex.o
      2054         26          0          0          0      14937   stm32f1xx_hal_uart.o
       532         74          0          0         32       7814   stm32f1xx_it.o
         2          0         24          4          0       1023   system_stm32f1xx.o
        52          6          0         24          0       1266   task.o
       468         40          0          0        216       2900   tim.o
      1216        228          0         12        532       3260   uart_app.o
       516         62          0          0        280       2889   usart.o

    ----------------------------------------------------------------------
     12000       <USER>        <GROUP>         64       2212     542363   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        28          0          2          2          6          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        36          0          0          0          0        108   memseta.o
       372         14          0          0          0        184   printf1.o
         0          0          0          4          0          0   stdout.o
        30          0          0          0          0         80   strncmp.o
        44          0          0          0          0         80   uidiv.o
        20          0          0          0          0         68   cfcmple.o
        20          0          0          0          0         68   cfrcmple.o
       176          0          0          0          0        140   fadd.o
       110          0          0          0          0        168   fepilogue.o
        50          0          0          0          0         68   ffixi.o
        40          0          0          0          0         68   ffixui.o
        18          0          0          0          0         68   fflti.o
       100          0          0          0          0         76   fmul.o

    ----------------------------------------------------------------------
      1112         <USER>          <GROUP>          4          0       1244   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       572         30          0          4          0        520   mc_w.l
       534          0          0          0          0        724   mf_w.l

    ----------------------------------------------------------------------
      1112         <USER>          <GROUP>          4          0       1244   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     13112       1044        312         68       2212     537259   Grand Totals
     13112       1044        312         68       2212     537259   ELF Image Totals
     13112       1044        312         68          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13424 (  13.11kB)
    Total RW  Size (RW Data + ZI Data)              2280 (   2.23kB)
    Total ROM Size (Code + RO Data + RW Data)      13492 (  13.18kB)

==============================================================================

